Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB16840000 ntdll.dll
7FFB15360000 KERNEL32.DLL
7FFB14190000 KERNELBASE.dll
7FFB15700000 USER32.dll
7FFB13C00000 win32u.dll
000210040000 msys-2.0.dll
7FFB15900000 GDI32.dll
7FFB14050000 gdi32full.dll
7FFB139D0000 msvcp_win.dll
7FFB13C30000 ucrtbase.dll
7FFB14C40000 advapi32.dll
7FFB14E00000 msvcrt.dll
7FFB14FA0000 sechost.dll
7FFB166E0000 RPCRT4.dll
7FFB130E0000 CRYPTBASE.DLL
7FFB14560000 bcryptPrimitives.dll
7FFB150C0000 IMM32.DLL
