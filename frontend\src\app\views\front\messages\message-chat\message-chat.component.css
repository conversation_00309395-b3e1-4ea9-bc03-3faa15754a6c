/* Variables CSS */
:root {
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-primary: #1a1a2e;
  --dark-accent: rgba(255, 255, 255, 0.1);
  --modern-white: #ffffff;
  --modern-gray-200: #e5e7eb;
  --glass-effect: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --neon-orange: #ff6600;
  --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 18px;
  --border-radius-xl: 20px;
  --error-color: #ff3b30;
  --text-dim: #aaa;
  --accent-color: #00f7ff;
}

/* En-tête */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--accent-gradient);
  border-bottom: 1px solid var(--glass-border);
  height: 60px;
  box-shadow: var(--shadow-modern);
  position: relative;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-primary);
  background-image: var(--accent-gradient);
  border-bottom: 1px solid var(--dark-accent);
}

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff00; /* Remplace var(--neon-green) non définie */
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: var(--dark-primary);
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
}

:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}

.whatsapp-status {
  font-size: 0.75rem;
  color: #666;
}

:host-context(.dark) .whatsapp-status {
  color: var(--text-dim);
}

/* Boutons d'action */

.whatsapp-actions {
  display: flex;
  gap: 16px;
}

.whatsapp-action-button {
  background: transparent;
  border: none;
  color: var(--accent-color);
  width: 42px;
  height: 42px;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
  font-size: 1.1rem;
}

/* Boutons d'appel avec forme circulaire */
.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50% !important;
  background: rgba(0, 247, 255, 0.1) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
}

.whatsapp-action-button:hover {
  transform: scale(1.1) translateY(-2px);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

:host-context(.dark) .whatsapp-action-button {
  color: var(--accent-color);
  text-shadow: 0 0 10px var(--accent-color);
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: white;
  text-shadow: none;
}

/* Boutons d'envoi */
.whatsapp-send-button,
.whatsapp-voice-button {
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  width: 44px;
  height: 44px;
  background: var(--accent-gradient);
}

.whatsapp-voice-button {
  background: var(--neon-orange);
  width: 46px;
  height: 46px;
  font-size: 1.2rem;
  margin-left: 12px;
}

.whatsapp-send-button:hover:not(:disabled),
.whatsapp-voice-button:hover {
  transform: scale(1.15);
}

.whatsapp-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-primary);
  border-top: 1px solid var(--dark-accent);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

/* Formulaire et outils d'entrée */
.whatsapp-input-form {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.whatsapp-input-tools {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

.whatsapp-tool-button {
  width: 38px;
  height: 38px;
  background: transparent;
  border: none;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 1rem;
}

.whatsapp-tool-button:hover,
.whatsapp-tool-button.active {
  transform: scale(1.1);
  color: white;
  background: var(--accent-color);
}

/* Champs d'entrée */
.whatsapp-input-field {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: #333;
  font-size: 0.9375rem;
  transition: var(--transition-fast);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:host-context(.dark) .whatsapp-input-field {
  background-color: #3a3a3a;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.whatsapp-input-field:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Interface d'appel */
.active-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9998;
}

.video-call-interface,
.audio-call-interface,
.minimized-call-interface {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-call-interface {
  background: #000;
}

.audio-call-interface,
.minimized-call-interface {
  background: var(--accent-gradient);
}

.minimized-call-interface {
  justify-content: space-between;
  padding: 1rem;
  color: white;
}

/* Styles vidéo */
.remote-video,
.local-video,
.local-avatar {
  border-radius: 12px;
  object-fit: cover;
}

.remote-video {
  width: 100%;
  height: 100%;
}

.local-video,
.local-avatar {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
}

.call-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
}

.control-buttons,
.minimized-controls {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.minimized-controls {
  gap: 0.5rem;
}

/* Boutons de contrôle */
.control-btn,
.minimized-btn {
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.control-btn {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
}

.minimized-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.end-call,
.minimized-btn.end-call {
  background: #ff6b6b;
}

.control-btn.end-call:hover,
.minimized-btn:hover {
  transform: scale(1.15);
}

.minimized-info {
  display: flex;
  flex-direction: column;
}

/* Responsive */
@media (max-width: 768px) {
  .local-video,
  .local-avatar {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .call-controls {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

.notification-badge {
  background: var(--accent-gradient);
  border-radius: 6px;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  min-width: 18px;
  height: 18px;
  z-index: 10;
}

/* Optimisations */
* {
  box-sizing: border-box;
}

@media (pointer: coarse) {
  .whatsapp-action-button,
  .whatsapp-send-button,
  .whatsapp-voice-button {
    min-width: 44px;
    min-height: 44px;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms;
    transition-duration: 0.01ms;
  }
}
