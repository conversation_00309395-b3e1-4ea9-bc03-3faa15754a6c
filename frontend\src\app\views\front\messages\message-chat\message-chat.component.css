/* Variables CSS optimisées */
:root {
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --message-gradient: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.9),
    rgba(131, 56, 236, 0.9)
  );
  --dark-primary: #1a1a2e;
  --dark-accent: rgba(255, 255, 255, 0.1);
  --modern-white: #ffffff;
  --modern-gray-200: #e5e7eb;
  --glass-effect: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --neon-orange: #ff6600;
  --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 18px;
  --border-radius-xl: 20px;
  --error-color: #ff3b30;
  --text-dim: #aaa;
  --accent-color: #00f7ff;
}

/* Messages unifiés */
.futuristic-message-current-user .futuristic-message-bubble {
  background: var(--message-gradient);
  color: white;
  border: 1px solid var(--glass-border);
  transition: var(--transition-fast);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 4px
    var(--border-radius-lg);
}

.futuristic-message-current-user .futuristic-message-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 247, 255, 0.5);
}

.futuristic-message-other-user .futuristic-message-bubble {
  background: var(--modern-white);
  border: 1px solid rgba(0, 247, 255, 0.15);
  color: #333;
  transition: var(--transition-fast);
  border-radius: var(--border-radius-lg) var(--border-radius-lg)
    var(--border-radius-lg) 4px;
}

.futuristic-message-other-user .futuristic-message-bubble:hover {
  border-color: rgba(0, 247, 255, 0.25);
  box-shadow: 0 2px 15px rgba(0, 247, 255, 0.2);
}

/* En-tête */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--accent-gradient);
  border-bottom: 1px solid var(--glass-border);
  height: 60px;
  box-shadow: var(--shadow-modern);
  position: relative;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-primary);
  background-image: var(--accent-gradient);
  border-bottom: 1px solid var(--dark-accent);
}

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff00; /* Remplace var(--neon-green) non définie */
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: var(--dark-primary);
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333; /* Remplace var(--text-primary) non définie */
}

:host-context(.dark) .whatsapp-username {
  color: var(--dark-text);
}

.whatsapp-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

:host-context(.dark) .whatsapp-status {
  color: var(--text-dim);
}

/* ========================================
   BOUTONS D'ACTION UNIFIÉS
   ======================================== */

.whatsapp-actions {
  display: flex;
  gap: 16px;
}

.whatsapp-action-button {
  background: transparent;
  border: none;
  color: var(--accent-color);
  width: 42px;
  height: 42px;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-smooth);
  position: relative;
  font-size: 1.1rem;
}

/* Boutons d'appel avec forme circulaire */
.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50% !important;
  background: rgba(0, 247, 255, 0.1) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
}

.whatsapp-action-button:hover {
  transform: scale(1.1) translateY(-2px);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

:host-context(.dark) .whatsapp-action-button {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: white;
  text-shadow: none;
}

/* Mode sombre simplifié */
:host-context(.dark) .futuristic-messages-container {
  background: var(--dark-primary);
}

/* Scrollbars simplifiées */
.futuristic-messages-container::-webkit-scrollbar {
  width: 5px;
}

.futuristic-messages-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: var(--border-radius-sm);
}

/* Wrapper de message futuriste */
.futuristic-message-wrapper {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

/* Message futuriste style WhatsApp */
.futuristic-message {
  display: flex;
  align-items: flex-end;
  margin-bottom: 1px;
  position: relative;
  width: 100%;
}

/* Bulle de message simplifiée */
.futuristic-message-bubble {
  margin-bottom: 0.25rem;
  max-width: 70%;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
  border-radius: 12px;
  transition: var(--transition-fast);

  box-shadow: var(--shadow-modern);
}

.futuristic-message-content {
  max-width: 80%;
}

.futuristic-message-text {
  word-break: break-word;
  line-height: 1.5;
}

/* Séparateur de date futuriste */
.futuristic-date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem 0;
  color: var(--text-dim);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.futuristic-date-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0.3;
}

.futuristic-date-text {
  margin: 0 10px;
  padding: 2px 8px;
  background-color: rgba(0, 247, 255, 0.05);
  border-radius: var(--border-radius-sm);
}

/* Heure du message futuriste */
.futuristic-message-time {
  font-size: 0.7rem;
  margin-top: 0.2rem;
  opacity: 0.7;
  color: var(--text-dim);
}

/* Informations du message futuriste */
.futuristic-message-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 4px;
  font-size: 0.75rem;
  letter-spacing: 0.02em;
  font-weight: 300;
}

.futuristic-message-current-user .futuristic-message-info {
  color: rgba(255, 255, 255, 0.8);
}

.futuristic-message-other-user .futuristic-message-info {
  color: rgba(0, 247, 255, 0.7);
}

.futuristic-message-status {
  color: rgba(0, 247, 255, 0.9);
}

/* Messages utilisateur simplifiés */
.futuristic-message-current-user {
  justify-content: flex-end;
  display: flex;
}

.futuristic-message-current-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 80%;
}

.futuristic-message-current-user .futuristic-message-bubble {
  background: var(--accent-gradient);
  color: white;
  border-radius: 18px 18px 4px 18px;
}

/* Messages autres utilisateurs ultra-simplifiés */
.futuristic-message-other-user {
  justify-content: flex-start;
  display: flex;
}

.futuristic-message-other-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 80%;
}

.futuristic-message-other-user .futuristic-message-bubble {
  background: var(--modern-white);
  color: #333;
  border: 1px solid var(--modern-gray-200);
}

:host-context(.dark) .futuristic-message-other-user .futuristic-message-bubble {
  background: rgba(30, 30, 40, 0.7);
  color: #e0e0e0;
  border: 1px solid var(--dark-accent);
}

.futuristic-message-other-user .futuristic-message-bubble:hover {
  transform: translateY(-2px);
  background: rgba(230, 235, 245, 0.9);
  box-shadow: 0 6px 20px rgba(79, 95, 173, 0.2);
}

:host-context(.dark)
  .futuristic-message-other-user
  .futuristic-message-bubble:hover {
  background: rgba(40, 40, 50, 0.9);
  box-shadow: 0 6px 20px rgba(0, 247, 255, 0.3);
}

/* Zone de saisie */
.futuristic-input-container {
  padding: 6px 10px;
  background-color: var(--glass-effect);
  min-height: 50px;
  position: relative;
  z-index: 10;
  border-top: 1px solid var(--glass-border);
}

.futuristic-input-form {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.futuristic-input-tools {
  display: flex;
  gap: 8px;
}

/* Boutons d'envoi consolidés */
.futuristic-send-button,
.whatsapp-send-button,
.whatsapp-voice-button {
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  width: 44px;
  height: 44px;
  background: var(--accent-gradient);
}

.whatsapp-voice-button {
  background: var(--neon-orange);
  width: 46px;
  height: 46px;
  font-size: 1.2rem;
  margin-left: 12px;
}

.futuristic-send-button:hover,
.whatsapp-send-button:hover:not(:disabled),
.whatsapp-voice-button:hover {
  transform: scale(1.15);
}

.futuristic-send-button:disabled,
.whatsapp-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Conteneur principal */
.futuristic-chat-container {
  width: 100%;
  margin: 0 auto;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-modern);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

:host-context(:not(.dark)) .futuristic-chat-container {
  background: var(--modern-white);
  border: 1px solid var(--modern-gray-200);
}

:host-context(.dark) .futuristic-chat-container {
  background: var(--dark-primary);
  border: 1px solid var(--dark-accent);
}

/* Images simplifiées - Propriétés communes consolidées */
.futuristic-message-image-container,
.futuristic-image-wrapper,
.futuristic-message-image {
  transition: var(--transition-fast);
}

.futuristic-message-image-container {
  margin: 2px 0;
  max-width: 220px;
}

.futuristic-image-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-modern);
}

.futuristic-message-image {
  width: 100%;
  display: block;
  cursor: pointer;
}

.futuristic-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-fast);
  color: white;
  font-size: 1.5rem;
}

.futuristic-image-wrapper:hover .futuristic-image-overlay {
  opacity: 1;
}

.futuristic-image-wrapper:hover {
  transform: translateY(-2px);
}

/* Styles pour le conteneur d'image en plein écran - Optimisé */
.fullscreen-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-primary);
  border-top: 1px solid var(--dark-accent);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

/* Formulaire et outils d'entrée */
.whatsapp-input-form {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.whatsapp-input-tools {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

.whatsapp-tool-button,
.futuristic-tool-button {
  width: 38px;
  height: 38px;
  background: transparent;
  border: none;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 1rem;
}

.whatsapp-tool-button:hover,
.whatsapp-tool-button.active,
.futuristic-tool-button:hover,
.futuristic-tool-button.active {
  transform: scale(1.1);
  color: white;
  background: var(--accent-color);
}

/* Champs d'entrée consolidés */
.whatsapp-input-field,
.futuristic-input-field {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: #333;
  font-size: 0.9375rem;
  transition: var(--transition-fast);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:host-context(.dark) .whatsapp-input-field,
:host-context(.dark) .futuristic-input-field {
  background-color: #3a3a3a;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.whatsapp-input-field:focus,
.futuristic-input-field:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* États et messages */
.futuristic-error-container,
.futuristic-voice-message-container {
  display: flex;
  align-items: flex-start;
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
  margin: 15px;
  padding: 15px;
}

.futuristic-error-container {
  background: rgba(255, 0, 0, 0.1);
  border-left: 3px solid var(--error-color);
}

.futuristic-voice-message-container {
  min-width: 200px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: var(--shadow-modern);
  padding: 8px;
  margin: 0;
}

.futuristic-error-icon,
.futuristic-voice-play-button {
  color: var(--error-color);
  font-size: 1.25rem;
  margin-right: 15px;
}

.futuristic-error-title {
  color: var(--error-color);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.futuristic-error-message {
  color: var(--text-dim);
  font-size: 0.8125rem;
}

.futuristic-message-pending,
.futuristic-message-sending {
  opacity: 0.7;
}

.futuristic-message-error {
  border: 1px solid rgba(239, 68, 68, 0.5);
  opacity: 0.8;
}

.futuristic-voice-message {
  gap: 12px;
}

.futuristic-voice-waveform {
  gap: 3px;
  height: 36px;
}

.futuristic-voice-bar {
  width: 3px;
  background-color: currentColor;
  border-radius: 4px;
  transition: var(--transition-fast);
}

/* Interface d'appel */
.active-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9998;
}

.video-call-interface,
.audio-call-interface,
.minimized-call-interface {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-call-interface {
  background: #000;
}

.audio-call-interface,
.minimized-call-interface {
  background: var(--accent-gradient);
}

.minimized-call-interface {
  justify-content: space-between;
  padding: 1rem;
  color: white;
}

/* Styles vidéo ultra-consolidés */
.remote-video,
.local-video,
.local-avatar {
  border-radius: 12px;
  object-fit: cover;
}

.remote-video {
  width: 100%;
  height: 100%;
}

.local-video,
.local-avatar {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
}

.call-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
}

.control-buttons,
.minimized-controls {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.minimized-controls {
  gap: 0.5rem;
}

/* Boutons de contrôle */
.control-btn,
.minimized-btn {
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.control-btn {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
}

.minimized-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.end-call,
.minimized-btn.end-call {
  background: #ff6b6b;
}

.control-btn.end-call:hover,
.minimized-btn:hover {
  transform: scale(1.15);
}

.minimized-info {
  display: flex;
  flex-direction: column;
}

/* Responsive simplifié */
@media (max-width: 768px) {
  .local-video,
  .local-avatar {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .call-controls {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

/* Panneaux */
.notification-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-panel {
  width: 90%;
  max-width: 600px;
  height: 80%;
  background: var(--dark-primary);
  border-radius: 20px;
  box-shadow: var(--shadow-modern);
  border: 2px solid var(--glass-border);
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: var(--accent-gradient);
}

.notification-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: var(--glass-effect);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--glass-border);
  transition: var(--transition-fast);
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.notification-content {
  flex: 1;
}

/* Éléments interactifs */
.status-action-btn,
.item-action-btn,
.notification-btn {
  border: none;
  border-radius: 50%;
  background: var(--glass-effect);
  color: var(--text-dim);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.status-action-btn,
.item-action-btn {
  width: 36px;
  height: 36px;
}

.notification-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.status-action-btn:hover,
.item-action-btn:hover,
.notification-btn:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.notification-badge {
  background: var(--accent-gradient);
  border-radius: 6px;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  min-width: 18px;
  height: 18px;
  z-index: 10;
}

/* Boutons et contrôles consolidés */
.whatsapp-action-button,
.voice-play-btn-modern,
.futuristic-voice-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
}

.whatsapp-action-button {
  width: 44px;
  height: 44px;
  background: transparent;
  font-size: 1.1rem;
}

.voice-play-btn-modern,
.futuristic-voice-play-button {
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.voice-play-btn-modern:hover,
.futuristic-voice-play-button:hover {
  transform: scale(1.2);
}

.futuristic-voice-message,
.futuristic-voice-waveform {
  display: flex;
  align-items: center;
}

/* Menu consolidé */
.theme-selector-menu {
  display: block;
  visibility: visible;
  opacity: 1;
  z-index: 100;
  background: var(--glass-effect);
  border: 2px solid var(--glass-border);
  box-shadow: var(--shadow-modern);
}

.theme-selector-menu a {
  transition: var(--transition-fast);
  border-radius: var(--border-radius-sm);
  margin: 2px;
}

.theme-selector-menu a:hover {
  transform: translateX(5px);
  background: var(--accent-color);
}

/* Optimisations finales */
* {
  box-sizing: border-box;
}

@media (pointer: coarse) {
  .whatsapp-action-button,
  .whatsapp-send-button,
  .whatsapp-voice-button {
    min-width: 44px;
    min-height: 44px;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms;
    transition-duration: 0.01ms;
  }
}
