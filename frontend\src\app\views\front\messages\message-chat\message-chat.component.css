/* Variables CSS */
:root {
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-primary: #1a1a2e;
  --dark-accent: rgba(255, 255, 255, 0.1);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --neon-orange: #ff6600;
  --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --text-dim: #aaa;
  --accent-color: #00f7ff;
}

/* En-tête */
.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--accent-gradient);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  height: 60px;
  box-shadow: var(--shadow-modern);
  position: relative;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-primary);
  background-image: var(--accent-gradient);
  border-bottom: 1px solid var(--dark-accent);
}

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff00;
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: var(--dark-primary);
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
}

:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}

.whatsapp-status {
  font-size: 0.75rem;
  color: #666;
}

:host-context(.dark) .whatsapp-status {
  color: var(--text-dim);
}

/* Boutons d'action */
.whatsapp-actions {
  display: flex;
  gap: 16px;
}

.whatsapp-action-button {
  background: transparent;
  border: none;
  color: var(--accent-color);
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 1.1rem;
}

.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50%;
  background: rgba(0, 247, 255, 0.1);
  border: 1px solid rgba(0, 247, 255, 0.3);
}

.whatsapp-action-button:hover {
  transform: scale(1.1) translateY(-2px);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

:host-context(.dark) .whatsapp-action-button {
  color: var(--accent-color);
  text-shadow: 0 0 10px var(--accent-color);
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: white;
  text-shadow: none;
}

/* Boutons d'envoi */
.whatsapp-send-button,
.whatsapp-voice-button {
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  width: 44px;
  height: 44px;
  background: var(--accent-gradient);
}

.whatsapp-voice-button {
  background: var(--neon-orange);
  width: 46px;
  height: 46px;
  font-size: 1.2rem;
  margin-left: 12px;
}

.whatsapp-send-button:hover:not(:disabled),
.whatsapp-voice-button:hover {
  transform: scale(1.15);
}

.whatsapp-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-primary);
  border-top: 1px solid var(--dark-accent);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

/* Formulaire d'entrée */
.whatsapp-input-form {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.whatsapp-input-tools {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

.whatsapp-tool-button {
  width: 38px;
  height: 38px;
  background: transparent;
  border: none;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 1rem;
}

.whatsapp-tool-button:hover,
.whatsapp-tool-button.active {
  transform: scale(1.1);
  color: white;
  background: var(--accent-color);
}

/* Champs d'entrée */
.whatsapp-input-field {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: #333;
  font-size: 0.9375rem;
  transition: var(--transition-fast);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:host-context(.dark) .whatsapp-input-field {
  background-color: #3a3a3a;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.whatsapp-input-field:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.notification-badge {
  background: var(--accent-gradient);
  border-radius: 6px;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  min-width: 18px;
  height: 18px;
  z-index: 10;
}
